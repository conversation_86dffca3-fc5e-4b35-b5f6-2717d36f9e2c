// src/controllers/Tools/Business/legalContractController.js    

/**
 * @desc    Generate a service contract using AI
 * @route   POST /api/legal-contracts/generate
 * @access  Private
 */
export const generateServiceContract = async (req, res) => {
    const {
        providerName, providerEmail, providerAddress,
        clientName, clientEmail, clientAddress,
        effectiveDate, jurisdiction, contractLanguage,
        serviceDescription, deliverables, exclusions,
        projectPeriod, projectPhases, totalAmount, paymentSchedule, paymentMethods,
        additionalTerms,
        includeDeathClause, includeNonCompete, includeExecutiveSummary, customClauses
    } = req.body;

    // --- Enhanced AI Prompt Engineering with Tag System ---
    const prompt = `
        You are an expert legal assistant specializing in contract law. Your task is to generate a comprehensive, professionally formatted Service Contract based on the following detailed specifications.

        **IMPORTANT FORMATTING REQUIREMENTS:**
        You MUST use the following tag system for proper formatting:
        - ~H~ for main contract headers (e.g., "~H~ SERVICE AGREEMENT CONTRACT")
        - ~S~ for major section headers (e.g., "~S~ 1. PARTIES TO THE CONTRACT")
        - ~S_SUB~ for sub-section headers (e.g., "~S_SUB~ Service Provider Information")
        - ~P~ for paragraphs of content
        - ~L~ for list items
        - ~CLAUSE~ for special contract clauses
        - ~TERM~ for specific contract terms
        - ~FINANCIAL~ for financial terms (e.g., "~FINANCIAL~ Overtime Rate: $75 per hour")
        - ~TABLE~ for tables (e.g., parties information in table format)
        - ~SIGNATURE~ for signature blocks

        **Contract Language:** ${contractLanguage}
        **Governing Law and Jurisdiction:** The contract will be governed by the laws of ${jurisdiction}.

        ---
        ### **CONTRACT SPECIFICATIONS**

        **1. PARTIES INFORMATION:**
        - **Service Provider:**
            - Name: ${providerName}
            - Email: ${providerEmail}
            - Address: ${providerAddress}
        - **Client:**
            - Name: ${clientName}
            - Email: ${clientEmail}
            - Address: ${clientAddress}
        - **Effective Date:** ${effectiveDate}

        **2. SCOPE OF WORK:**
        - **Detailed Description of Services:**
            ${serviceDescription}
        - **Key Deliverables:**
            ${deliverables}
        - **Service Exclusions:**
            ${exclusions || 'None specified'}

        **3. TIMELINE & PAYMENT TERMS:**
        - **Project Duration:** ${projectPeriod}
        - **Project Phases & Milestones:**
            ${projectPhases}
        - **Total Contract Value:** ${totalAmount}
        - **Payment Schedule:**
            ${paymentSchedule}
        - **Accepted Payment Methods:** ${paymentMethods}
        - **Additional Financial Terms:**
            ${additionalTerms || 'None specified'}

        **4. OPTIONAL CLAUSES TO INCLUDE:**
        ${includeExecutiveSummary ? '- Include Executive Summary at the beginning' : ''}
        ${includeDeathClause ? '- Include Death/Disability provisions' : ''}
        ${includeNonCompete ? '- Include Non-Compete restrictions' : ''}
        ${customClauses ? `- Custom Terms: ${customClauses}` : ''}

        ---
        ### **GENERATION INSTRUCTIONS**

        Generate a complete, legally structured service contract that includes:
        1. Contract title and executive summary (if requested)
        2. Parties identification in TABLE format using ~TABLE~ tag
        3. Detailed scope of work and deliverables
        4. Timeline, milestones, and project phases
        5. Payment terms, schedule, and methods
        6. Financial terms using ~FINANCIAL~ tag for items like overtime rates, late fees, etc.
        7. Terms and conditions
        8. Optional protective clauses (as specified)
        9. Termination and dispute resolution clauses
        10. Signature blocks for both parties

        **FORMATTING EXAMPLES:**
        - Main title: ~H~ SERVICE AGREEMENT CONTRACT
        - Section: ~S~ 1. PARTIES TO THE CONTRACT
        - Table for parties:
        ~TABLE~ Field|Service Provider|Client
        Name|${providerName || 'Provider Name'}|${clientName || 'Client Name'}
        Email|${providerEmail || '<EMAIL>'}|${clientEmail || '<EMAIL>'}
        Address|${providerAddress || 'Provider Address'}|${clientAddress || 'Client Address'}
        - Financial term: ~FINANCIAL~ Overtime Rate: $75 per hour
        - Regular paragraph: ~P~ This agreement is entered into...
        - List item: ~L~ Source code and documentation
        - Clause: ~CLAUSE~ This agreement shall be governed by...
        - Signature: ~SIGNATURE~ IN WITNESS WHEREOF, the Parties have executed this Agreement as of the Effective Date.

        **CRITICAL TABLE FORMAT REQUIREMENTS:**
        - Start table with ~TABLE~ tag followed by header row
        - Each subsequent table row should be on a new line
        - Use pipe (|) to separate columns
        - Do NOT put any other tags between table rows
        - Table rows must contain actual data, not placeholders

        **CRITICAL:** Use ONLY the specified tag system for ALL formatting.

        Write the entire contract in ${contractLanguage} language.
        Ensure all legal terminology is appropriate for ${jurisdiction} jurisdiction.
        Make the contract comprehensive, professional, and legally sound.
    `;

    try {
        const generatedContract = await generateContent(prompt);
        res.status(200).json({ contract: generatedContract });
    } catch (error) {
        res.status(500).json({ message: "An error occurred while generating the contract.", error: error.message });
    }

};

