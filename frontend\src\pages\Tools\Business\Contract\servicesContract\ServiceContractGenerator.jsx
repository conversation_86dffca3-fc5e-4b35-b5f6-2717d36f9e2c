import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { selectAuthToken } from '../../../../../store/features/auth/authSlice';

// Storage utilities
import {
  saveContractToStorage,
  loadContractFromStorage,
  clearContractFromStorage,
  updateContractContent
} from './utils/contractStorage';

// Step Components
import Step1PartiesDetails from './steps/Step1PartiesDetails';
import Step2WorkScope from './steps/Step2WorkScope';
import Step3TimelinePayment from './steps/Step3TimelinePayment';
import Step4OptionalClauses from './steps/Step4OptionalClauses';
import Step5ContractResult from './steps/Step5ContractResult';

// Data
import { stepConfig, totalSteps } from './data/stepData';

const ServiceContractGenerator = () => {
  const navigate = useNavigate();
  const authToken = useSelector(selectAuthToken);
  
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({});
  const [contractContent, setContractContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Load contract from storage on component mount
  useEffect(() => {
    const storedContract = loadContractFromStorage();
    if (storedContract) {
      setFormData(storedContract.formData);
      setContractContent(storedContract.contract);
      setCurrentStep(5); // Go directly to results if contract exists
      console.log('[CONTRACT_GENERATOR] Loaded contract from storage');
    }
  }, []);

  // Form validation
  const validateStep = useCallback((step, data) => {
    const requiredFields = stepConfig[step]?.fields || [];
    
    switch (step) {
      case 1:
        return ['providerName', 'providerEmail', 'providerAddress', 'clientName', 'clientEmail', 'clientAddress', 'effectiveDate', 'jurisdiction', 'contractLanguage']
          .every(field => data[field] && data[field].trim() !== '');
      case 2:
        return ['serviceDescription', 'deliverables']
          .every(field => data[field] && data[field].trim() !== '');
      case 3:
        return ['projectPeriod', 'projectPhases', 'totalAmount', 'paymentSchedule', 'paymentMethods']
          .every(field => data[field] && data[field].trim() !== '');
      case 4:
        return true; // Optional step
      default:
        return true;
    }
  }, []);

  // Form data handlers
  const updateFormData = useCallback((stepData) => {
    setFormData(prev => ({ ...prev, ...stepData }));
  }, []);

  // Navigation handlers
  const handleNext = useCallback(() => {
    if (currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1);
    }
  }, [currentStep]);

  const handleBack = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  }, [currentStep]);

  // Contract generation
  const generateContract = useCallback(async () => {
    setIsLoading(true);
    setCurrentStep(5);

    try {
      const response = await fetch(`${import.meta.env.VITE_NODE_BACKEND_URL}/api/legal-contracts/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || `HTTP ${response.status}: Failed to generate contract`);
      }

      if (!data.contract) {
        throw new Error('No contract content received from server');
      }

      setContractContent(data.contract);
      saveContractToStorage(data.contract, formData);
      
    } catch (error) {
      console.error('Error generating contract:', error);
      
      let errorMessage = 'Error generating contract. Please try again.';
      
      if (error.message.includes('503')) {
        errorMessage = 'AI service is temporarily unavailable. Please try again later.';
      } else if (error.message.includes('429')) {
        errorMessage = 'Rate limit exceeded. Please wait a moment and try again.';
      } else if (error.message.includes('504')) {
        errorMessage = 'Request timed out. Please try again.';
      } else if (error.message.includes('Failed to fetch')) {
        errorMessage = 'Network error. Please check your connection and try again.';
      }
      
      setContractContent(`Error: ${errorMessage}`);
    } finally {
      setIsLoading(false);
    }
  }, [formData, authToken]);

  // Reset form
  const handleReset = useCallback(() => {
    setFormData({});
    setContractContent('');
    setCurrentStep(1);
    clearContractFromStorage(); // Clear from storage
  }, []);

  // Regenerate contract
  const handleRegenerate = useCallback(() => {
    generateContract();
  }, [generateContract]);

  // Step 4 to 5 transition (generate contract)
  const handleGenerateContract = useCallback(() => {
    generateContract();
  }, [generateContract]);

  // Check if current step is valid
  const isCurrentStepValid = validateStep(currentStep, formData);

  // Render current step
  const renderCurrentStep = () => {
    const stepProps = {
      formData,
      onChange: updateFormData,
      onNext: currentStep === 4 ? handleGenerateContract : handleNext,
      onBack: handleBack,
      isValid: isCurrentStepValid
    };

    switch (currentStep) {
      case 1:
        return <Step1PartiesDetails {...stepProps} />;
      case 2:
        return <Step2WorkScope {...stepProps} />;
      case 3:
        return <Step3TimelinePayment {...stepProps} />;
      case 4:
        return <Step4OptionalClauses {...stepProps} />;
      case 5:
        return (
          <Step5ContractResult
            formData={formData}
            contractContent={contractContent}
            isLoading={isLoading}
            onBack={handleBack}
            onRegenerate={handleRegenerate}
            onReset={handleReset}
          />
        );
      default:
        return <Step1PartiesDetails {...stepProps} />;
    }
  };

  return (
    <div className="min-h-screen  text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Progress Bar */}
        {currentStep < 5 && (
          <div className="max-w-4xl mx-auto mb-8">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm text-slate-400">Progress</span>
              <span className="text-sm text-slate-400">{currentStep} of {totalSteps}</span>
            </div>
            <div className="w-full bg-slate-800 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${(currentStep / totalSteps) * 100}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Current Step */}
        {renderCurrentStep()}
      </div>
    </div>
  );
};

export default ServiceContractGenerator;

