// Contract PDF Generator
// Based on the existing business plan PDF generator but optimized for contracts

import jsPDF from 'jspdf';
import { parseContractContent } from '../format/contractContentFormatter';
import { loadFonts } from '../../../components/utils/fontLoader';
// Import modular formatters
import { renderContractHeader, renderContractSection, renderSubSection } from './formatters/headerFormatter';
import { renderContractParagraph, renderPlainText } from './formatters/paragraphFormatter';
import { renderContractListItem } from './formatters/listFormatter';
import { renderContractTable, renderPartiesTable } from './formatters/tableFormatter';
import { renderContractClause, renderContractTerm, renderSignatureBlock } from './formatters/clauseFormatter';
import { renderFinancialTerm } from './formatters/financialFormatter';

// Professional PDF Configuration - Optimized for business documents
const PDF_CONFIG = {
  format: 'a4',
  orientation: 'portrait',
  unit: 'mm',
  margins: {
    top: 25,
    bottom: 25,
    left: 25,
    right: 25
  },
  colors: {
    primary: '#1e40af',      // Deep blue for main headers
    secondary: '#0891b2',    // Teal for section headers
    accent: '#059669',       // Green for lists and highlights
    text: '#111827',         // Very dark gray for body text
    textLight: '#374151',    // Medium gray for secondary text
    border: '#d1d5db',       // Light gray for borders
    tableBg: '#f8fafc',      // Very light blue-gray for table backgrounds
    tableHeader: '#1e293b',  // Dark slate for table headers
    tableHeaderText: '#e2e8f0', // Light text for table headers
    financial: '#dc2626',    // Red for financial terms
    clause: '#d97706',       // Orange for clauses
    signature: '#7c3aed'     // Purple for signature blocks
  },
  fonts: {
    regular: 'helvetica',
    bold: 'helvetica',
    italic: 'helvetica',
    arabic: 'helvetica'
  },
  spacing: {
    lineHeight: 4.5,         // Tight line spacing
    paragraphSpacing: 6,     // Minimal paragraph spacing
    sectionSpacing: 12,      // Moderate section spacing
    subsectionSpacing: 8,    // Tight subsection spacing
    listIndent: 15,          // Compact list indentation
    listItemSpacing: 3,      // Minimal list item spacing
    tableRowHeight: 8,       // Compact table rows
    beforeTable: 4,          // Space before tables
    afterTable: 8           // Space after tables
  },
  typography: {
    headerSize: 18,          // Main contract header
    sectionSize: 14,         // Section headers
    subsectionSize: 12,      // Subsection headers
    bodySize: 10,            // Body text
    tableSize: 9,            // Table text
    footerSize: 8            // Footer text
  }
};

/**
 * Generate PDF from contract content
 * @param {string} contractContent - The contract content with tags
 * @param {Object} formData - Form data used to generate the contract
 * @param {string} language - Contract language for RTL support
 * @returns {Promise<jsPDF>} - The generated PDF document
 */
export const generateContractPDF = async (contractContent, formData, language = 'English') => {
  try {
    // Initialize PDF
    const doc = new jsPDF({
      orientation: PDF_CONFIG.orientation,
      unit: PDF_CONFIG.unit,
      format: PDF_CONFIG.format
    });

    // Load fonts
    await loadFonts(doc);

    // Set RTL support if needed
    const isRTL = ['Arabic', 'Hebrew', 'Persian', 'Urdu'].includes(language);
    if (isRTL) {
      doc.internal.isRtl = true;
    }

    // Parse contract content
    const parsedElements = parseContractContent(contractContent);

    // Set initial position
    let currentY = PDF_CONFIG.margins.top;
    const pageHeight = doc.internal.pageSize.getHeight();
    const maxY = pageHeight - PDF_CONFIG.margins.bottom;

    // Add contract metadata
    doc.setProperties({
      title: `Service Contract - ${formData.providerName || 'Provider'} & ${formData.clientName || 'Client'}`,
      subject: 'Service Agreement Contract',
      author: formData.providerName || 'Contract Generator',
      creator: 'AI Contract Generator',
      producer: 'Legal Contract Generator'
    });

    // Render each element
    for (let i = 0; i < parsedElements.length; i++) {
      const element = parsedElements[i];

      // Check if we need a new page
      if (currentY > maxY - 40) {
        doc.addPage();
        currentY = PDF_CONFIG.margins.top;
      }

      switch (element.type) {
        case 'header':
          currentY = renderContractHeader(doc, element.content, currentY, PDF_CONFIG);
          currentY += PDF_CONFIG.spacing.sectionSpacing;
          break;
        case 'section':
          currentY = renderContractSection(doc, element.content, currentY, PDF_CONFIG);
          currentY += PDF_CONFIG.spacing.subsectionSpacing;
          break;
        case 'subsection':
          currentY = renderContractSection(doc, element.content, currentY, PDF_CONFIG, true);
          currentY += PDF_CONFIG.spacing.subsectionSpacing;
          break;
        case 'paragraph':
          currentY = renderContractParagraph(doc, element.content, currentY, PDF_CONFIG);
          currentY += PDF_CONFIG.spacing.paragraphSpacing;
          break;
        case 'listItem':
          currentY = renderContractListItem(doc, element.content, currentY, PDF_CONFIG);
          currentY += PDF_CONFIG.spacing.listItemSpacing;
          break;
        case 'clause':
          currentY = renderContractClause(doc, element.content, currentY, PDF_CONFIG);
          currentY += PDF_CONFIG.spacing.paragraphSpacing;
          break;
        case 'term':
          currentY = renderContractTerm(doc, element.content, currentY, PDF_CONFIG);
          currentY += PDF_CONFIG.spacing.paragraphSpacing;
          break;
        case 'financial':
          currentY = renderFinancialTerm(doc, element.content, currentY, PDF_CONFIG);
          currentY += PDF_CONFIG.spacing.paragraphSpacing;
          break;
        case 'table':
          currentY += PDF_CONFIG.spacing.beforeTable;
          currentY = renderContractTable(doc, element.content, currentY, PDF_CONFIG);
          currentY += PDF_CONFIG.spacing.afterTable;
          break;
        case 'signature':
          currentY = renderSignatureBlock(doc, element.content, currentY, PDF_CONFIG);
          currentY += PDF_CONFIG.spacing.sectionSpacing;
          break;
        case 'text':
          currentY = renderPlainText(doc, element.content, currentY, PDF_CONFIG);
          currentY += PDF_CONFIG.spacing.paragraphSpacing;
          break;
        default:
          // Handle unknown types as paragraphs
          currentY = renderContractParagraph(doc, element.content, currentY, PDF_CONFIG);
          currentY += PDF_CONFIG.spacing.paragraphSpacing;
          break;
      }
    }

    // Add footer with generation info
    addContractFooter(doc, formData, language);

    return doc;
  } catch (error) {
    console.error('Error generating contract PDF:', error);
    throw new Error('Failed to generate PDF: ' + error.message);
  }
};

/**
 * Add footer to contract PDF
 * @param {jsPDF} doc - PDF document
 * @param {Object} formData - Form data
 * @param {string} language - Contract language
 */
const addContractFooter = (doc, formData, language) => {
  const pageCount = doc.internal.getNumberOfPages();
  const pageHeight = doc.internal.pageSize.getHeight();
  const pageWidth = doc.internal.pageSize.getWidth();

  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    
    // Set footer font
    doc.setFont(PDF_CONFIG.fonts.regular);
    doc.setFontSize(8);
    doc.setTextColor(PDF_CONFIG.colors.textLight);

    // Add page number
    const pageText = `Page ${i} of ${pageCount}`;
    const pageTextWidth = doc.getTextWidth(pageText);
    doc.text(pageText, pageWidth - PDF_CONFIG.margins.right - pageTextWidth, pageHeight - 10);

    // Add generation info on first page
    if (i === 1) {
      const generationText = `Generated on ${new Date().toLocaleDateString()} by AI Contract Generator`;
      doc.text(generationText, PDF_CONFIG.margins.left, pageHeight - 10);
    }
  }
};

/**
 * Download contract as PDF
 * @param {string} contractContent - The contract content
 * @param {Object} formData - Form data
 * @param {string} language - Contract language
 */
export const downloadContractPDF = async (contractContent, formData, language = 'English') => {
  try {
    const doc = await generateContractPDF(contractContent, formData, language);
    
    // Generate filename
    const providerName = (formData.providerName || 'Provider').replace(/[^a-zA-Z0-9]/g, '_');
    const clientName = (formData.clientName || 'Client').replace(/[^a-zA-Z0-9]/g, '_');
    const date = new Date().toISOString().split('T')[0];
    const filename = `Service_Contract_${providerName}_${clientName}_${date}.pdf`;
    
    // Download the PDF
    doc.save(filename);
    
    console.log(`[CONTRACT_PDF] Downloaded: ${filename}`);
  } catch (error) {
    console.error('Error downloading contract PDF:', error);
    throw error;
  }
};
